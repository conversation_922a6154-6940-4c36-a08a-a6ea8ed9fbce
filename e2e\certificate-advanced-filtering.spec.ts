import { expect, test } from '@playwright/test'

test.describe('Certificate Advanced Filtering', () => {
  // Use the saved authentication state
  test.use({ storageState: 'e2e/auth-state.json' })

  test.beforeEach(async ({ page }) => {
    // Clean up any existing test data
    await page.request.post('/api/admin/cleanup-test-data', {
      headers: { 'Content-Type': 'application/json' },
      data: { action: 'cleanup_certificates' }
    })

    // Seed fresh test data
    await page.request.post('/api/admin/seed-notifications', {
      headers: { 'Content-Type': 'application/json' },
      data: { action: 'seed_test_data' }
    })

    // Navigate to certificates page
    await page.goto('/certificates')

    // Wait for the page to load completely
    await page.waitForLoadState('networkidle')

    // Wait for the certificates page to be ready by checking for the main heading
    await page.waitForSelector('h1:has-text("Certificates")', { timeout: 10000 })

    // Wait for the search input to be visible
    await page.waitForSelector('[data-testid="certificates-search"]', { timeout: 10000 })
  })

  test.describe('Date Range Filtering', () => {
    test('should display date range filter controls', async ({ page }) => {
      // Look for date range filter button or controls
      const dateFilterButton = page.locator('[data-testid="date-filter-button"]')

      if (await dateFilterButton.isVisible()) {
        await dateFilterButton.click()

        // Check for date inputs
        await expect(page.locator('input[type="date"]').first()).toBeVisible()
        await expect(page.locator('input[type="date"]').nth(1)).toBeVisible()
      } else {
        // If not implemented yet, skip this test
        test.skip('Date range filtering not yet implemented')
      }
    })

    test('should filter by expiry date range', async ({ page }) => {
      const dateFilterButton = page.locator('[data-testid="date-filter-button"]')

      if (await dateFilterButton.isVisible()) {
        await dateFilterButton.click()

        // Set date range (next 6 months)
        const today = new Date()
        const sixMonthsLater = new Date(today.getFullYear(), today.getMonth() + 6, today.getDate())

        const fromDate = page.locator('input[name="dateFrom"]')
        const toDate = page.locator('input[name="dateTo"]')

        await fromDate.fill(today.toISOString().split('T')[0])
        await toDate.fill(sixMonthsLater.toISOString().split('T')[0])

        // Apply filter
        await page.locator('[data-testid="apply-date-filter"]').click()
        await page.waitForTimeout(500)

        // Verify filtering is applied
        const cards = page.locator('[data-testid="certificate-card"]')
        const count = await cards.count()

        // Results should only show certificates expiring in the date range
        if (count > 0) {
          // This would need to verify actual expiry dates in the UI
          expect(count).toBeGreaterThanOrEqual(0)
        }
      } else {
        test.skip('Date range filtering not yet implemented')
      }
    })

    test('should clear date range filter', async ({ page }) => {
      const dateFilterButton = page.locator('[data-testid="date-filter-button"]')

      if (await dateFilterButton.isVisible()) {
        await dateFilterButton.click()

        // Set some dates
        const fromDate = page.locator('input[name="dateFrom"]')
        await fromDate.fill('2024-01-01')

        // Clear filter
        const clearButton = page.locator('[data-testid="clear-date-filter"]')
        if (await clearButton.isVisible()) {
          await clearButton.click()

          // Verify dates are cleared
          const fromValue = await fromDate.inputValue()
          expect(fromValue).toBe('')
        }
      } else {
        test.skip('Date range filtering not yet implemented')
      }
    })
  })

  test.describe('Tag-Based Filtering', () => {
    test('should display available tags for filtering', async ({ page }) => {
      // Look for tag filter controls
      const tagFilterButton = page.locator('[data-testid="tag-filter-button"]')

      if (await tagFilterButton.isVisible()) {
        await tagFilterButton.click()

        // Should show available tags
        const tagOptions = page.locator('[data-testid="tag-option"]')
        const tagCount = await tagOptions.count()

        if (tagCount > 0) {
          // Verify tags are clickable
          await expect(tagOptions.first()).toBeVisible()
        }
      } else {
        // Check if tags are displayed inline
        const inlineTags = page.locator('[data-testid="filter-tag"]')
        const inlineCount = await inlineTags.count()

        if (inlineCount > 0) {
          await expect(inlineTags.first()).toBeVisible()
        } else {
          test.skip('Tag filtering not yet implemented')
        }
      }
    })

    test('should filter by selected tags', async ({ page }) => {
      // Look for tag filtering options
      const tagElements = page.locator('[data-testid="filter-tag"], [data-testid="tag-option"]')
      const tagCount = await tagElements.count()

      if (tagCount > 0) {
        // Get initial certificate count
        const initialCards = page.locator('[data-testid="certificate-card"]')
        const initialCount = await initialCards.count()

        // Click on first available tag
        await tagElements.first().click()
        await page.waitForTimeout(500)

        // Verify filtering is applied
        const filteredCards = page.locator('[data-testid="certificate-card"]')
        const filteredCount = await filteredCards.count()

        // Should have same or fewer results
        expect(filteredCount).toBeLessThanOrEqual(initialCount)

        // If there are results, they should contain the selected tag
        if (filteredCount > 0) {
          const firstCard = filteredCards.first()
          const cardText = await firstCard.textContent()
          // This would need to verify the tag appears in the certificate
          expect(cardText).toBeTruthy()
        }
      } else {
        test.skip('Tag filtering not yet implemented')
      }
    })

    test('should support multiple tag selection', async ({ page }) => {
      const tagElements = page.locator('[data-testid="filter-tag"], [data-testid="tag-option"]')
      const tagCount = await tagElements.count()

      if (tagCount >= 2) {
        // Select first tag
        await tagElements.first().click()
        await page.waitForTimeout(300)

        const firstFilterCount = await page.locator('[data-testid="certificate-card"]').count()

        // Select second tag
        await tagElements.nth(1).click()
        await page.waitForTimeout(300)

        const secondFilterCount = await page.locator('[data-testid="certificate-card"]').count()

        // Multiple tag selection should work (either AND or OR logic)
        expect(secondFilterCount).toBeGreaterThanOrEqual(0)
      } else {
        test.skip('Multiple tag filtering not available')
      }
    })

    test('should clear tag filters', async ({ page }) => {
      const tagElements = page.locator('[data-testid="filter-tag"], [data-testid="tag-option"]')
      const tagCount = await tagElements.count()

      if (tagCount > 0) {
        // Get initial count
        const initialCards = page.locator('[data-testid="certificate-card"]')
        const initialCount = await initialCards.count()

        // Apply tag filter
        await tagElements.first().click()
        await page.waitForTimeout(300)

        // Look for clear button
        const clearButton = page.locator('[data-testid="clear-tag-filters"], text=Clear')

        if (await clearButton.isVisible()) {
          await clearButton.click()
          await page.waitForTimeout(300)

          // Should return to original count
          const clearedCards = page.locator('[data-testid="certificate-card"]')
          const clearedCount = await clearedCards.count()
          expect(clearedCount).toBe(initialCount)
        }
      } else {
        test.skip('Tag filtering not yet implemented')
      }
    })
  })

  test.describe('Filter Combinations', () => {
    test('should combine date and tag filters', async ({ page }) => {
      const dateFilterButton = page.locator('[data-testid="date-filter-button"]')
      const tagElements = page.locator('[data-testid="filter-tag"], [data-testid="tag-option"]')

      const hasDateFilter = await dateFilterButton.isVisible()
      const hasTagFilter = await tagElements.count() > 0

      if (hasDateFilter && hasTagFilter) {
        // Apply date filter
        await dateFilterButton.click()
        const fromDate = page.locator('input[name="dateFrom"]')
        await fromDate.fill('2024-01-01')
        await page.locator('[data-testid="apply-date-filter"]').click()
        await page.waitForTimeout(300)

        const dateFilterCount = await page.locator('[data-testid="certificate-card"]').count()

        // Apply tag filter
        await tagElements.first().click()
        await page.waitForTimeout(300)

        const combinedCount = await page.locator('[data-testid="certificate-card"]').count()

        // Combined filter should have same or fewer results
        expect(combinedCount).toBeLessThanOrEqual(dateFilterCount)
      } else {
        test.skip('Advanced filtering combinations not yet implemented')
      }
    })

    test('should combine all filter types', async ({ page }) => {
      // Get initial count
      const initialCards = page.locator('[data-testid="certificate-card"]')
      const initialCount = await initialCards.count()

      // Apply search
      const searchInput = page.locator('[data-testid="certificates-search"]')
      await searchInput.fill('Training')
      await page.waitForTimeout(300)

      // Apply status filter
      await page.locator('[data-testid="filter-expiring-soon"]').click()
      await page.waitForTimeout(300)

      // Apply sort
      await page.locator('[data-testid="sort-button"]').click()
      await page.locator('text=Name').click()
      await page.waitForTimeout(300)

      // Apply tag filter if available
      const tagElements = page.locator('[data-testid="filter-tag"], [data-testid="tag-option"]')
      const tagCount = await tagElements.count()

      if (tagCount > 0) {
        await tagElements.first().click()
        await page.waitForTimeout(300)
      }

      // Verify all filters are working together
      const finalCards = page.locator('[data-testid="certificate-card"]')
      const finalCount = await finalCards.count()

      // Should have same or fewer results than initial
      expect(finalCount).toBeLessThanOrEqual(initialCount)

      // If there are results, verify they match the search term
      if (finalCount > 0) {
        const allText = await finalCards.allTextContents()
        const hasSearchTerm = allText.some(text => text.toLowerCase().includes('training'))
        expect(hasSearchTerm).toBe(true)
      }
    })
  })

  test.describe('Filter State Management', () => {
    test('should preserve filters on page refresh', async ({ page }) => {
      // Apply search
      const searchInput = page.locator('[data-testid="certificates-search"]')
      await searchInput.fill('Safety')
      await page.waitForTimeout(300)

      // Apply filter
      await page.locator('[data-testid="filter-favorites"]').click()
      await page.waitForTimeout(300)

      // Get current URL (should contain filter state)
      const currentUrl = page.url()

      // Refresh page
      await page.reload()
      await page.waitForLoadState('networkidle')

      // Check if filters are preserved
      const searchValue = await searchInput.inputValue()

      // URL-based state management would preserve search
      if (currentUrl.includes('search=') || currentUrl.includes('filter=')) {
        expect(searchValue).toBe('Safety')
      } else {
        // If not implemented, filters might not persist
        console.log('Filter state persistence not yet implemented')
      }
    })

    test('should update URL with filter parameters', async ({ page }) => {
      // Apply search
      const searchInput = page.locator('[data-testid="certificates-search"]')
      await searchInput.fill('Training')
      await page.waitForTimeout(300)

      // Check if URL is updated
      const urlWithSearch = page.url()

      // Apply filter
      await page.locator('text=Expiring Soon').click()
      await page.waitForTimeout(300)

      const urlWithFilter = page.url()

      // URLs should be different if state management is implemented
      if (urlWithSearch !== urlWithFilter || urlWithFilter.includes('search=') || urlWithFilter.includes('filter=')) {
        expect(urlWithFilter).toContain('certificates')
      } else {
        console.log('URL state management not yet implemented')
      }
    })
  })

  test.describe('Filter Performance', () => {
    test('should handle large result sets efficiently', async ({ page }) => {
      // Clear any existing filters
      await page.locator('[data-testid="filter-all"]').click()
      await page.waitForTimeout(300)

      // Measure time for filter application
      const startTime = Date.now()

      // Apply a filter that might return many results
      await page.locator('[data-testid="filter-favorites"]').click()
      await page.waitForLoadState('networkidle')

      const endTime = Date.now()
      const filterTime = endTime - startTime

      // Filter should complete within reasonable time (2 seconds)
      expect(filterTime).toBeLessThan(2000)

      // Verify results are displayed
      const cards = page.locator('[data-testid="certificate-card"]')
      await expect(cards.first()).toBeVisible({ timeout: 1000 })
    })

    test('should debounce search input', async ({ page }) => {
      const searchInput = page.locator('[data-testid="certificates-search"]')

      // Type quickly
      await searchInput.fill('T')
      await searchInput.fill('Tr')
      await searchInput.fill('Tra')
      await searchInput.fill('Train')
      await searchInput.fill('Training')

      // Wait for debounce
      await page.waitForTimeout(600)

      // Should only trigger search after debounce period
      const cards = page.locator('[data-testid="certificate-card"]')
      await expect(cards.first()).toBeVisible({ timeout: 1000 })
    })
  })
})
