/**
 * User Management Functions
 *
 * This module handles all user-related database operations including:
 * - User CRUD operations
 * - User profile management
 * - User statistics and queries
 * - Account deletion and recovery
 */

import { neon } from "@neondatabase/serverless"
import { and, count, desc, eq, gte, ilike, isNotNull, isNull, or } from "drizzle-orm"
import { drizzle } from "drizzle-orm/neon-http"
import { nanoid } from "nanoid"
import {
  accountDeletionAudits,
  cleanupJobs,
  users
} from "./schema"

// Initialize database connection for this module
const sql = neon(process.env.DATABASE_URL!)
const db = drizzle(sql)

// ============================================================================
// BASIC USER OPERATIONS
// ============================================================================

/**
 * Get user by email address
 */
export async function getUserByEmail(email: string) {
  try {
    const result = await db.select().from(users).where(eq(users.email, email))
    return result[0] || null
  } catch (error) {
    console.error("Error getting user by email:", error)
    return null
  }
}

/**
 * Get user by ID
 */
export async function getUserById(id: string) {
  try {
    const result = await db.select().from(users).where(eq(users.id, id))
    return result[0] || null
  } catch (error) {
    console.error("Error getting user by ID:", error)
    return null
  }
}

/**
 * Create a new user
 */
export async function createUser(userData: {
  id: string
  name: string
  email: string
  password?: string
  role?: string
  subscriptionPlan?: string
  tenantId?: string
  tenantRole?: string
  emailVerified?: boolean
}) {
  try {
    const now = new Date()
    await db.insert(users).values({
      ...userData,
      role: userData.role || "individual_user", // Default to INDIVIDUAL_USER
      subscriptionPlan: userData.subscriptionPlan || "individual_free", // Default plan
      emailVerified: userData.emailVerified || false,
      createdAt: now,
      updatedAt: now,
    })
    return { success: true }
  } catch (error) {
    console.error("Error creating user:", error)
    throw error
  }
}

/**
 * Update user profile information
 */
export async function updateUser(
  userId: string,
  userData: Partial<{
    name: string
    email: string
    role: string
    subscriptionPlan: string
    tenantId: string
    tenantRole: string
    emailVerified: boolean
    twoFactorEnabled: boolean
    twoFactorSecret: string
    lastLoginAt: Date
  }>
) {
  try {
    await db
      .update(users)
      .set({
        ...userData,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId))
    return { success: true }
  } catch (error) {
    console.error("Error updating user:", error)
    throw error
  }
}

/**
 * Update user's last login timestamp
 */
export async function updateUserLastLogin(userId: string) {
  try {
    await db
      .update(users)
      .set({
        lastLoginAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId))
    return { success: true }
  } catch (error) {
    console.error("Error updating user last login:", error)
    throw error
  }
}

// ============================================================================
// USER STATISTICS AND QUERIES
// ============================================================================

/**
 * Get user statistics for admin dashboard
 */
export async function getUserStats() {
  try {
    const totalUsers = await db.select({ count: count() }).from(users)
    const activeUsers = await db
      .select({ count: count() })
      .from(users)
      .where(isNull(users.deletedAt))

    const deletedUsers = await db
      .select({ count: count() })
      .from(users)
      .where(isNotNull(users.deletedAt))

    const verifiedUsers = await db
      .select({ count: count() })
      .from(users)
      .where(and(eq(users.emailVerified, true), isNull(users.deletedAt)))

    // Recent users (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentUsers = await db
      .select({ count: count() })
      .from(users)
      .where(and(gte(users.createdAt, thirtyDaysAgo), isNull(users.deletedAt)))

    return {
      total: totalUsers[0].count,
      active: activeUsers[0].count,
      deleted: deletedUsers[0].count,
      verified: verifiedUsers[0].count,
      recent: recentUsers[0].count,
    }
  } catch (error) {
    console.error("Error getting user stats:", error)
    return {
      total: 0,
      active: 0,
      deleted: 0,
      verified: 0,
      recent: 0,
    }
  }
}

/**
 * Get all users with pagination for admin dashboard
 */
export async function getAllUsers(limit = 50, offset = 0) {
  try {
    const allUsers = await db
      .select()
      .from(users)
      .orderBy(desc(users.createdAt))
      .limit(limit)
      .offset(offset)

    return allUsers
  } catch (error) {
    console.error("Error getting all users:", error)
    return []
  }
}

/**
 * Search users by email or name with proper SQL queries
 */
export async function searchUsers(query: string, limit = 20) {
  try {
    // Use proper SQL ILIKE queries for case-insensitive search
    const searchPattern = `%${query}%`

    const searchResults = await db
      .select()
      .from(users)
      .where(
        and(
          isNull(users.deletedAt),
          or(
            ilike(users.email, searchPattern),
            ilike(users.name, searchPattern)
          )
        )
      )
      .orderBy(desc(users.createdAt))
      .limit(limit)

    return searchResults
  } catch (error) {
    console.error("Error searching users:", error)
    return []
  }
}

/**
 * Get users by role
 */
export async function getUsersByRole(role: string) {
  try {
    const result = await db
      .select()
      .from(users)
      .where(and(eq(users.role, role), isNull(users.deletedAt)))
      .orderBy(desc(users.createdAt))

    return result
  } catch (error) {
    console.error("Error getting users by role:", error)
    return []
  }
}

/**
 * Get recently active users
 */
export async function getRecentlyActiveUsers(days = 30, limit = 50) {
  try {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - days)

    const result = await db
      .select()
      .from(users)
      .where(
        and(
          isNull(users.deletedAt),
          gte(users.lastLoginAt!, cutoffDate)
        )
      )
      .orderBy(desc(users.lastLoginAt))
      .limit(limit)

    return result
  } catch (error) {
    console.error("Error getting recently active users:", error)
    return []
  }
}

// ============================================================================
// ACCOUNT DELETION AND RECOVERY
// ============================================================================

/**
 * Request account deletion (soft delete with 30-day recovery period)
 */
export async function requestAccountDeletion(
  userId: string,
  reason?: string,
  ipAddress?: string,
  userAgent?: string
) {
  try {
    const user = await getUserById(userId)
    if (!user) {
      throw new Error("User not found")
    }

    if (user.deletedAt) {
      throw new Error("Account is already marked for deletion")
    }

    const now = new Date()
    const deletionToken = nanoid(32)
    const deletionTokenExpires = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000)) // 30 days

    // Mark user for deletion
    await db
      .update(users)
      .set({
        deletedAt: now,
        deletionRequestedAt: now,
        deletionReason: reason || null,
        deletionToken,
        deletionTokenExpires,
        updatedAt: now,
      })
      .where(eq(users.id, userId))

    // Create audit record
    await createAccountDeletionAudit({
      userId,
      userEmail: user.email,
      userName: user.name,
      userRole: user.role,
      deletionType: "soft",
      deletionReason: reason,
      initiatedBy: "user",
      initiatorId: userId,
      ipAddress,
      userAgent,
    })

    // Schedule cleanup job for 30 days from now
    await scheduleAccountCleanup(userId, deletionTokenExpires)

    return {
      success: true,
      deletionToken,
      recoveryDeadline: deletionTokenExpires
    }
  } catch (error) {
    console.error("Error requesting account deletion:", error)
    throw error
  }
}

/**
 * Recover account from soft deletion
 */
export async function recoverAccount(deletionToken: string) {
  try {
    const user = await db
      .select()
      .from(users)
      .where(eq(users.deletionToken, deletionToken))
      .limit(1)

    if (!user[0]) {
      throw new Error("Invalid recovery token")
    }

    const userData = user[0]

    if (!userData.deletedAt) {
      throw new Error("Account is not marked for deletion")
    }

    if (!userData.deletionTokenExpires || new Date() > userData.deletionTokenExpires) {
      throw new Error("Recovery token has expired")
    }

    // Restore the account
    await db
      .update(users)
      .set({
        deletedAt: null,
        deletionRequestedAt: null,
        deletionReason: null,
        deletionToken: null,
        deletionTokenExpires: null,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userData.id))

    // Create audit record
    await createAccountDeletionAudit({
      userId: userData.id,
      userEmail: userData.email,
      userName: userData.name,
      userRole: userData.role,
      deletionType: "recovered",
      deletionReason: "Account recovered by user",
      initiatedBy: "user",
      initiatorId: userData.id,
    })

    return { success: true }
  } catch (error) {
    console.error("Error recovering account:", error)
    throw error
  }
}

/**
 * Create account deletion audit record
 */
export async function createAccountDeletionAudit(auditData: {
  userId: string
  userEmail: string
  userName: string
  userRole: string
  deletionType: "soft" | "hard" | "recovered"
  deletionReason?: string
  initiatedBy: "user" | "admin" | "system"
  initiatorId?: string
  dataRetained?: string
  dataDeleted?: string
  certificateCount?: number
  fileCount?: number
  ipAddress?: string
  userAgent?: string
}) {
  try {
    await db.insert(accountDeletionAudits).values({
      id: nanoid(),
      ...auditData,
      certificateCount: auditData.certificateCount || 0,
      fileCount: auditData.fileCount || 0,
    })
    return { success: true }
  } catch (error) {
    console.error("Error creating account deletion audit:", error)
    throw error
  }
}

/**
 * Schedule account cleanup job
 */
export async function scheduleAccountCleanup(userId: string, scheduledFor: Date) {
  try {
    const jobId = nanoid()
    const now = new Date()
    await db.insert(cleanupJobs).values({
      id: jobId,
      jobType: "account_deletion",
      status: "pending",
      scheduledFor,
      metadata: JSON.stringify({ userId }),
      createdAt: now,
      updatedAt: now,
    })
    return { success: true, jobId }
  } catch (error) {
    console.error("Error scheduling account cleanup:", error)
    throw error
  }
}
