/**
 * Unit tests for organization registration functionality
 * Tests the database functions used in organization registration
 */

import { addUserToOrganization, createOrganization } from '@/lib/db'

// Mock the database module
jest.mock('@/lib/db', () => ({
  createOrganization: jest.fn(),
  addUserToOrganization: jest.fn(),
}))

const mockCreateOrganization = createOrganization as jest.MockedFunction<typeof createOrganization>
const mockAddUserToOrganization = addUserToOrganization as jest.MockedFunction<typeof addUserToOrganization>

describe('Organization Registration Database Functions', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockCreateOrganization.mockResolvedValue({ success: true })
    mockAddUserToOrganization.mockResolvedValue({ success: true })
  })

  describe('createOrganization', () => {
    it('should create yacht company organization successfully', async () => {
      const organizationData = {
        id: 'org_123',
        name: 'Elite Yacht Services',
        type: 'yacht_company' as const,
        contactEmail: '<EMAIL>',
        description: 'Premium yacht management services',
        website: 'https://eliteyachts.com',
      }

      await createOrganization(organizationData)

      expect(mockCreateOrganization).toHaveBeenCalledWith(organizationData)
      expect(mockCreateOrganization).toHaveBeenCalledTimes(1)
    })

    it('should create certification provider organization successfully', async () => {
      const organizationData = {
        id: 'org_456',
        name: 'Maritime Training Institute',
        type: 'cert_provider' as const,
        contactEmail: '<EMAIL>',
        description: 'Professional maritime certification',
      }

      await createOrganization(organizationData)

      expect(mockCreateOrganization).toHaveBeenCalledWith(organizationData)
      expect(mockCreateOrganization).toHaveBeenCalledTimes(1)
    })
  })

  describe('addUserToOrganization', () => {
    it('should add user as organization owner successfully', async () => {
      const membershipData = {
        id: 'membership_123',
        userId: 'user_456',
        organizationId: 'org_789',
        role: 'owner' as const,
      }

      await addUserToOrganization(membershipData)

      expect(mockAddUserToOrganization).toHaveBeenCalledWith(membershipData)
      expect(mockAddUserToOrganization).toHaveBeenCalledTimes(1)
    })

    it('should add user as organization admin successfully', async () => {
      const membershipData = {
        id: 'membership_456',
        userId: 'user_789',
        organizationId: 'org_123',
        role: 'admin' as const,
        invitedBy: 'owner_user_id',
      }

      await addUserToOrganization(membershipData)

      expect(mockAddUserToOrganization).toHaveBeenCalledWith(membershipData)
      expect(mockAddUserToOrganization).toHaveBeenCalledTimes(1)
    })

    it('should add user as organization member successfully', async () => {
      const membershipData = {
        id: 'membership_789',
        userId: 'user_123',
        organizationId: 'org_456',
        role: 'member' as const,
        invitedBy: 'admin_user_id',
      }

      await addUserToOrganization(membershipData)

      expect(mockAddUserToOrganization).toHaveBeenCalledWith(membershipData)
      expect(mockAddUserToOrganization).toHaveBeenCalledTimes(1)
    })
  })

  describe('Error Handling', () => {
    it('should handle organization creation failure gracefully', async () => {
      mockCreateOrganization.mockRejectedValue(new Error('Database error'))

      const organizationData = {
        id: 'org_123',
        name: 'Test Organization',
        type: 'yacht_company' as const,
        contactEmail: '<EMAIL>',
      }

      await expect(createOrganization(organizationData)).rejects.toThrow('Database error')
    })

    it('should handle membership creation failure gracefully', async () => {
      mockAddUserToOrganization.mockRejectedValue(new Error('Membership error'))

      const membershipData = {
        id: 'membership_123',
        userId: 'user_456',
        organizationId: 'org_789',
        role: 'owner' as const,
      }

      await expect(addUserToOrganization(membershipData)).rejects.toThrow('Membership error')
    })
  })
})
